"""Enhanced LLM Client with LiteLLM Backend
Provides unified access to 100+ LLM providers with automatic model detection
"""

import logging
import os
import time
from typing import Any, Dict, List

import litellm

logger = logging.getLogger(__name__)


class LiteLLMClient:
    """Enhanced LLM client with LiteLLM backend supporting 100+ providers"""

    def __init__(self, config: dict[str, Any]):
        """Initialize the enhanced LLM client

        Args:
            config: Configuration dict with keys:
                - api_key: API key for the provider
                - model: Model name (e.g., 'qwen/qwen3-coder:free')
                - provider: Provider name (auto-detected if not specified)
                - temperature: Temperature setting (default: 0.1)
                - max_retries: Max retry attempts (default: 3)
                - api_base: Custom API base URL (optional)

        """
        self.provider = config.get("provider", "openrouter")
        self.model = config.get("model", "openrouter/qwen/qwen3-coder:free")
        self.api_key = config.get("api_key") or os.environ.get("OPENROUTER_API_KEY", "")
        self.temperature = config.get("temperature", 0.1)
        self.max_retries = config.get("max_retries", 3)
        self.api_base = config.get("api_base")
        self.max_tokens = config.get("max_tokens", 262144)
        self.max_output_tokens = config.get("max_output_tokens")

        self._setup_environment(config)

        logger.info(
            "Initialized LiteLLM client: %s/%s",
            self.provider,
            self.model,
        )

    def _setup_environment(self, config: dict[str, Any]) -> None:
        """Setup environment variables for the detected provider"""
        if self.provider == "openrouter":
            # Set OpenRouter environment variables
            if self.api_key:
                os.environ["OPENROUTER_API_KEY"] = self.api_key

            # Set optional OpenRouter parameters
            os.environ.setdefault(
                "OR_SITE_URL",
                "https://github.com/onezibo/fuzzlm-agent",
            )
            os.environ.setdefault("OR_APP_NAME", "FuzzLM-Agent")

            if self.api_base:
                os.environ["OPENROUTER_API_BASE"] = self.api_base

        elif self.provider == "anthropic":
            if self.api_key:
                os.environ["ANTHROPIC_API_KEY"] = self.api_key

        elif self.provider == "openai":
            if self.api_key:
                os.environ["OPENAI_API_KEY"] = self.api_key

        logger.debug("Environment setup completed for provider: %s", self.provider)

    def generate(self, prompt: str, **kwargs: Any) -> str:
        """Generate a response from the LLM

        Args:
            prompt: The prompt text
            **kwargs: Additional parameters (max_tokens, temperature, etc.)

        Returns:
            The generated response text

        """
        # Prepare LiteLLM parameters
        params = self._prepare_litellm_params(prompt, **kwargs)

        # Make the request with retries
        return self._make_request_with_retries(params)

    def _prepare_litellm_params(self, prompt: str, **kwargs: Any) -> dict[str, Any]:
        """Prepare parameters for LiteLLM completion call"""
        return {
            "model": self.model,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": kwargs.get("temperature", self.temperature),
            "max_tokens": (
                self.max_output_tokens
                if self.max_output_tokens
                else (
                    self.max_tokens
                    - self._calculate_max_tokens([{"role": "user", "content": prompt}])
                )
                * 0.8
            ),
        }

    def _calculate_max_tokens(self, messages: List[Dict[str, Any]]) -> int:
        """Calculate the maximum number of tokens to generate"""
        try:
            # 使用 litellm.token_counter 计算 token 数量
            return litellm.token_counter(model=self.model, messages=messages)  # type: ignore[attr-defined]
        except Exception:
            print(
                f"Failed to calculate max tokens with model {self.model}, using default model",
            )
            # 如果失败，使用默认模型重试
            return litellm.token_counter(model="gpt-3.5-turbo", messages=messages)  # type: ignore[attr-defined]

    def _make_request_with_retries(self, params: dict[str, Any]) -> str:
        """Make LiteLLM request with retry logic"""
        last_error = None

        for attempt in range(self.max_retries):
            try:
                # Call LiteLLM in a thread pool to avoid blocking
                response = litellm.completion(**params)

                # Extract content from response
                if hasattr(response, "choices") and response.choices:
                    content = response.choices[0].message.content
                    return str(content)
                msg = "Invalid response format from LiteLLM"
                raise Exception(msg)

            except Exception as e:
                error_msg = str(e)
                logger.warning(
                    "LiteLLM request failed (attempt %d/%d): %s",
                    attempt + 1,
                    self.max_retries,
                    error_msg,
                )

                last_error = e

                # Handle rate limiting with exponential backoff
                if "rate" in error_msg.lower() or "429" in error_msg:
                    wait_time = 2**attempt
                    logger.info("Rate limited, waiting %ds before retry", wait_time)
                    time.sleep(wait_time)
                elif attempt < self.max_retries - 1:
                    # Short delay for other errors
                    time.sleep(1)

        # All retries failed
        raise last_error or Exception("All retry attempts failed")
